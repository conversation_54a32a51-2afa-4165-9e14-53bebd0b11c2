<?php
/**
 * 🚀 БЫСТРЫЙ API для автомобилей с использованием кэша
 *
 * Этот API использует предварительно созданные кэш-файлы для мгновенного отклика
 * Поддерживает приоритетную сортировку по 4-уровневой системе брендов
 */

// Увеличиваем лимит памяти для обработки больших файлов
ini_set('memory_limit', '4G');
ini_set('max_execution_time', 300); // 5 минут

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

/**
 * Упрощенная функция для чтения больших JSON файлов по частям
 */
function readLargeJsonFile($filePath, $filters = [], $limit = 0) {
    if (!file_exists($filePath)) {
        return [];
    }

    // Для очень больших файлов используем простое чтение по частям
    $fileSize = filesize($filePath);
    if ($fileSize > 500 * 1024 * 1024) { // Больше 500MB
        return readJsonInChunks($filePath, $filters, $limit);
    }

    // Для файлов среднего размера читаем обычным способом
    $content = file_get_contents($filePath);
    $data = json_decode($content, true);
    unset($content); // Освобождаем память

    $cars = $data['cars'] ?? [];
    unset($data); // Освобождаем память

    $result = [];
    $foundCars = 0;

    foreach ($cars as $car) {
        if (empty($filters) || matchesFilters($car, $filters)) {
            $result[] = $car;
            $foundCars++;

            if ($limit > 0 && $foundCars >= $limit) {
                break;
            }
        }
    }

    return $result;
}

/**
 * Чтение JSON файла по частям для очень больших файлов
 */
function readJsonInChunks($filePath, $filters = [], $limit = 0) {
    // Для файлов больше 500MB используем временное решение - возвращаем ограниченное количество
    // Это предотвращает ошибки памяти, но ограничивает результаты

    // Увеличиваем лимит памяти временно
    $oldMemoryLimit = ini_get('memory_limit');
    ini_set('memory_limit', '2G');

    try {
        $content = file_get_contents($filePath);
        $data = json_decode($content, true);
        unset($content); // Освобождаем память

        $cars = $data['cars'] ?? [];
        unset($data); // Освобождаем память

        $result = [];
        $foundCars = 0;

        // Ограничиваем обработку для больших файлов
        $maxProcess = min(count($cars), 50000); // Обрабатываем максимум 50k записей

        for ($i = 0; $i < $maxProcess; $i++) {
            $car = $cars[$i];
            if (empty($filters) || matchesFilters($car, $filters)) {
                $result[] = $car;
                $foundCars++;

                if ($limit > 0 && $foundCars >= $limit) {
                    break;
                }
            }
        }

        return $result;
    } catch (Exception $e) {
        // В случае ошибки возвращаем пустой массив
        return [];
    } finally {
        // Восстанавливаем лимит памяти
        ini_set('memory_limit', $oldMemoryLimit);
    }
}

/**
 * Быстрая проверка соответствия фильтрам
 */
function matchesFilters($car, $filters) {
    // Проверка забронированных автомобилей
    $price = (int)($car['price'] ?? 0);
    if ($price === 999999 || $price === 333333) {
        return false;
    }

    // Проверка ошибочных цен
    if ($price <= 0 || ($price > 0 && $price < 100) || $price > 50000) {
        return false;
    }

    // Фильтр по категории (самый важный для производительности)
    if (!empty($filters['category'])) {
        $carBrand = $car['brand'] ?? $car['mark'] ?? '';
        $carModel = $car['model'] ?? '';
        $carGeneration = $car['generation'] ?? '';
        $carCategory = determineCarCategory($carBrand, $carModel, $carGeneration);
        if ($carCategory !== $filters['category']) {
            return false;
        }
    }

    return true;
}

/**
 * Оптимизированная функция для загрузки кэша по приоритету
 */
function loadCacheByPriority($priority, $date, $filters = [], $limit = 0) {
    $cacheDir = __DIR__ . "/../../cache";

    // Ищем чанки для этого приоритета
    $chunkFiles = glob($cacheDir . "/super_priority_{$priority}_chunk_*_{$date}.json");

    // Если чанков нет, пробуем старый формат
    if (empty($chunkFiles)) {
        $oldCacheFile = $cacheDir . "/super_priority_{$priority}_{$date}.json";
        if (file_exists($oldCacheFile)) {
            $chunkFiles = [$oldCacheFile];
        } else {
            return [];
        }
    }

    $allCars = [];
    $foundCars = 0;

    // Обрабатываем каждый чанк
    foreach ($chunkFiles as $chunkFile) {
        $fileSize = filesize($chunkFile);

        // Пропускаем слишком большие файлы (больше 200MB)
        if ($fileSize > 200 * 1024 * 1024) {
            continue;
        }

        $cacheData = json_decode(file_get_contents($chunkFile), true);
        $cars = $cacheData['cars'] ?? [];

        // Применяем фильтры если нужно
        if (!empty($filters)) {
            foreach ($cars as $car) {
                if (matchesFilters($car, $filters)) {
                    $allCars[] = $car;
                    $foundCars++;
                    if ($limit > 0 && $foundCars >= $limit) {
                        return $allCars;
                    }
                }
            }
        } else {
            // Если фильтров нет, просто добавляем автомобили
            foreach ($cars as $car) {
                $allCars[] = $car;
                $foundCars++;
                if ($limit > 0 && $foundCars >= $limit) {
                    return $allCars;
                }
            }
        }
    }

    return $allCars;
}

/**
 * 🚀 НОВАЯ ФУНКЦИЯ: Загрузка ВСЕХ автомобилей из указанных приоритетов
 * Используется для категорий business/sport/suv для загрузки всех доступных автомобилей
 */
function loadAllCacheByPriorities($priorities, $date, $filters = []) {
    $cacheDir = __DIR__ . "/../../cache";
    $allCars = [];

    foreach ($priorities as $priority) {
        // Ищем чанки для этого приоритета
        $chunkFiles = glob($cacheDir . "/super_priority_{$priority}_chunk_*_{$date}.json");

        // Если чанков нет, пробуем старый формат
        if (empty($chunkFiles)) {
            $oldCacheFile = $cacheDir . "/super_priority_{$priority}_{$date}.json";
            if (file_exists($oldCacheFile)) {
                $chunkFiles = [$oldCacheFile];
            }
        }

        // Обрабатываем каждый чанк для данного приоритета
        foreach ($chunkFiles as $chunkFile) {
            $fileSize = filesize($chunkFile);

            // УБИРАЕМ ограничение по размеру файла для загрузки всех автомобилей
            // Увеличиваем лимит памяти для больших файлов
            $oldMemoryLimit = ini_get('memory_limit');
            ini_set('memory_limit', '2G');

            try {
                $cacheData = json_decode(file_get_contents($chunkFile), true);
                $cars = $cacheData['cars'] ?? [];

                // Применяем фильтры если нужно
                if (!empty($filters)) {
                    foreach ($cars as $car) {
                        if (matchesFilters($car, $filters)) {
                            // Добавляем приоритет к каждому автомобилю
                            $car['brand_priority'] = $priority;
                            $car['priority_category'] = getPriorityName($priority);
                            $allCars[] = $car;
                        }
                    }
                } else {
                    // Если фильтров нет, просто добавляем автомобили
                    foreach ($cars as $car) {
                        // Добавляем приоритет к каждому автомобилю
                        $car['brand_priority'] = $priority;
                        $car['priority_category'] = getPriorityName($priority);
                        $allCars[] = $car;
                    }
                }

                // Освобождаем память
                unset($cacheData, $cars);

            } catch (Exception $e) {
                // В случае ошибки продолжаем с следующим файлом
                continue;
            } finally {
                // Восстанавливаем лимит памяти
                ini_set('memory_limit', $oldMemoryLimit);
            }
        }
    }

    return $allCars;
}

/**
 * Функция для поиска самой свежей даты кэша
 */
function findLatestCacheDate() {
    $cacheDir = __DIR__ . "/../../cache";

    if (!is_dir($cacheDir)) {
        return null;
    }

    // Ищем все файлы мастер-индекса
    $masterIndexFiles = glob($cacheDir . "/master_index_*.json");

    if (empty($masterIndexFiles)) {
        // Если нет мастер-индексов, ищем любые файлы кэша с датой
        $cacheFiles = glob($cacheDir . "/super_priority_*_chunk_*_*.json");

        if (empty($cacheFiles)) {
            // Ищем старые форматы кэша
            $cacheFiles = glob($cacheDir . "/priority_*_*.json");
        }

        if (empty($cacheFiles)) {
            return null;
        }

        // Извлекаем даты из имен файлов
        $dates = [];
        foreach ($cacheFiles as $file) {
            if (preg_match('/(\d{4}-\d{2}-\d{2})\.json$/', $file, $matches)) {
                $dates[] = $matches[1];
            }
        }

        if (empty($dates)) {
            return null;
        }

        // Возвращаем самую свежую дату
        rsort($dates);
        return $dates[0];
    }

    // Извлекаем даты из файлов мастер-индекса
    $dates = [];
    foreach ($masterIndexFiles as $file) {
        if (preg_match('/master_index_(\d{4}-\d{2}-\d{2})\.json$/', $file, $matches)) {
            $dates[] = $matches[1];
        }
    }

    if (empty($dates)) {
        return null;
    }

    // Возвращаем самую свежую дату
    rsort($dates);
    return $dates[0];
}

/**
 * Функция для загрузки индекса брендов
 */
function loadBrandIndex($date) {
    $indexFile = __DIR__ . "/../../cache/brand_index_{$date}.json";

    if (!file_exists($indexFile)) {
        return [];
    }

    return json_decode(file_get_contents($indexFile), true);
}

/**
 * Функция для перевода марок автомобилей
 */
function translateBrand($brand) {
    if (empty($brand)) return '';

    $brandTranslations = [
        '현대' => 'Hyundai',
        '기아' => 'Kia',
        '쌍용' => 'SsangYong',
        '르노삼성' => 'Renault Samsung',
        '제네시스' => 'Genesis',
        '쉐보레' => 'Chevrolet',
        '대우' => 'Daewoo',
        '쉐보레대우' => 'Chevrolet',
        '토요타' => 'Toyota',
        '닛산' => 'Nissan',
        '혼다' => 'Honda',
        'BMW' => 'BMW',
        '벤츠' => 'Mercedes-Benz',
        '아우디' => 'Audi',
        '폭스바겐' => 'Volkswagen',
        '볼보' => 'Volvo',
        '포드' => 'Ford',
        '렉서스' => 'Lexus',
        '미니' => 'MINI',
        '포르쉐' => 'Porsche',
        '랜드로버' => 'Land Rover',
        '재규어' => 'Jaguar',
        '크라이슬러' => 'Chrysler',
        '지프' => 'Jeep',
        '마세라티' => 'Maserati',
        '벤틀리' => 'Bentley',
        '페라리' => 'Ferrari',
        '람보르기니' => 'Lamborghini'
    ];

    // Проверка на известные модели, требующие особой маркировки
    if (strpos(strtolower($brand), 'matiz') !== false) {
        return 'Chevrolet';
    }

    return $brandTranslations[$brand] ?? $brand;
}

/**
 * Функция для определения категории автомобиля по марке и модели
 */
function determineCarCategory($brand, $model, $generation = '') {
    // Переводим марку для правильного сравнения
    $translatedBrand = translateBrand($brand);

    // Объединяем модель и поколение для более точного определения
    $fullModel = trim($model . ' ' . $generation);

    // Определения категорий автомобилей
    $categories = [
        'business' => [
            // Ультра-премиум бренды - только топовые модели
            'Rolls-Royce' => ['Phantom', 'Ghost', 'Wraith', 'Dawn', 'Spectre', 'Cullinan', 'Silver Seraph', 'Silver Shadow', 'Corniche'],
            'Bentley' => ['Flying Spur', 'Mulsanne', 'Arnage', 'Azure', 'Brooklands'],
            'Mercedes-Maybach' => ['S-Class', 'Maybach S-Class', 'Maybach', 'GLS', 'S 680', 'S 580', 'GLS 600', 'EQS SUV'],
            'Maybach' => ['S-Class', 'S 680', 'S 580', 'GLS', 'GLS 600', 'EQS SUV'],
            'Maserati' => ['Quattroporte', 'Ghibli', 'Levante', 'GranTurismo'],
            'Cadillac' => ['CT6', 'XTS', 'Escalade', 'Escalade ESV', 'Celestiq', 'CT5-V', 'CTS-V', 'ELR'],
            'Bugatti' => ['Chiron', 'Veyron', 'Divo', 'Centodieci', 'La Voiture Noire'],
            'Koenigsegg' => ['Regera', 'Jesko', 'CC850', 'Gemera', 'Agera'],
            'Pagani' => ['Huayra', 'Zonda', 'Utopia'],
            'Alpina' => ['B7', 'B8', 'XB7', 'B5', 'B6'],
            'Brabus' => ['S-Class', 'Maybach', 'G-Class', '800', '900'],
            'Lucid Motors' => ['Air', 'Air Dream', 'Air Touring', 'Air Pure'],
            'Rimac' => ['Nevera', 'Concept One'],
            'Pininfarina' => ['Battista', 'PF0'],
            'Spyker' => ['C8', 'B6', 'C12'],
            'Aston Martin' => ['Rapide', 'Lagonda', 'DB12'],
            'McLaren' => ['GT', '720S Spider'],
            'BMW' => ['7 Series', '8 Series', 'i7', '3 Series', '5 Series', '6 Series', 'X3', 'X5', 'X6'],
            'Mercedes-Benz' => ['S-Class', 'CLS', 'EQS', 'C-Class', 'E-Class', 'GLC', 'GLE'],
            'Range Rover' => ['Range Rover', 'Range Rover Sport', 'Range Rover Velar', 'Range Rover Evoque', 'Autobiography', 'Vogue', 'HSE'],
            'Lexus' => ['LS', 'LC', 'ES', 'GS', 'IS', 'RX', 'NX'],
            'Genesis' => ['G90', 'G80', 'G70'],
            'Jaguar' => ['XJ', 'XF'],
            'Volvo' => ['S60', 'S90', 'XC60', 'XC90', 'V60', 'V90'],
            'Acura' => ['TLX', 'MDX', 'RDX', 'ILX']
        ],
        'sport' => [
            'Ferrari' => ['458', '488', '812', 'F8', 'Roma', 'SF90', 'Portofino', 'LaFerrari', 'F12', 'California'],
            'Lamborghini' => ['Aventador', 'Huracan', 'Gallardo', 'Murcielago'],
            'Porsche' => ['911', '718 Boxster', '718 Cayman', 'Boxster', 'Cayman', 'GT2', 'GT3'],
            'McLaren' => ['570S', '720S', 'Artura', 'Elva', 'Senna', 'Speedtail'],
            'Aston Martin' => ['DB11', 'DB12', 'Vantage', 'DBS', 'Valkyrie', 'Valhalla'],
            'BMW' => ['M2', 'M3', 'M4', 'M5', 'M8', 'i8', 'Z4', '1 Series', '2 Series', '3 Series', '4 Series', '6 Series', 'X1', 'X2', 'X4'],
            'Audi' => ['R8', 'RS7', 'RS6', 'RS5', 'RS3', 'TT RS', 'A3', 'A4', 'A5', 'S3', 'S4', 'S5', 'Q3', 'Q5', 'TT'],
            'Mercedes-Benz' => ['AMG GT', 'C63 AMG', 'E63 AMG', 'SL', 'SLK', 'AMG', 'A-Class', 'C-Class', 'E-Class', 'CLA', 'CLS'],
            'Ford' => ['Mustang', 'GT'],
            'Nissan' => ['GT-R', '370Z', 'GTR'],
            'Toyota' => ['GR Supra', 'GR86', 'Supra'],
            'Jaguar' => ['F-Type'],
            'Maserati' => ['MC20'],
            'Lotus' => ['Elise', 'Emira', 'Evija', 'Evora', 'Exige'],
            'Bentley' => ['Continental GT', 'Continental'],
            'Lexus' => ['IS', 'RC', 'GS', 'CT', 'NX'],
            'Infiniti' => ['Q50', 'Q60', 'G35', 'G37', '350Z', '370Z'],
            'Alfa Romeo' => ['Giulia', 'Stelvio', '4C'],
            'Subaru' => ['WRX', 'STI', 'BRZ'],
            'Mazda' => ['MX-5', 'RX-7', 'RX-8']
        ],
        'suv' => [
            'BMW' => ['X5', 'X6', 'X7', 'X3', 'X4', 'X1', 'X2', 'X5 M', 'X6 M', 'iX'],
            'Mercedes-Benz' => ['GLS', 'GLE', 'G-Class', 'GLC', 'GLA', 'GLB', 'G'],
            'Audi' => ['Q7', 'Q8', 'Q5', 'Q3', 'RS Q8', 'SQ7', 'SQ5'],
            'Range Rover' => ['Range Rover', 'Range Rover Sport', 'Range Rover Velar', 'Range Rover Evoque'],
            'Land Rover' => ['Defender', 'Discovery', 'Range Rover', 'Range Rover Sport', 'Range Rover Velar', 'Range Rover Evoque'],
            'Porsche' => ['Cayenne', 'Macan'],
            'Lamborghini' => ['Urus'],
            'Aston Martin' => ['DBX'],
            'Maserati' => ['Grecale'],
            'Ferrari' => ['Purosangue'],
            'Cadillac' => ['XT5', 'XT6', 'XT4'],
            'Lexus' => ['LX', 'RX', 'GX', 'NX', 'UX'],
            'Toyota' => ['Land Cruiser', 'Highlander', 'RAV4', '4Runner'],
            'Volvo' => ['XC90', 'XC60', 'XC40'],
            'Jeep' => ['Grand Cherokee', 'Cherokee', 'Wrangler', 'Compass'],
            'Bentley' => ['Bentayga']
        ]
    ];

    // Проверяем каждую категорию
    foreach ($categories as $categoryName => $brands) {
        if (isset($brands[$translatedBrand])) {
            $models = $brands[$translatedBrand];
            foreach ($models as $categoryModel) {
                // Проверяем точное совпадение модели (без учета регистра)
                if (strcasecmp($model, $categoryModel) === 0) {
                    return $categoryName;
                }
                // Проверяем полную модель с поколением
                if (strcasecmp($fullModel, $categoryModel) === 0) {
                    return $categoryName;
                }
                // Проверяем, начинается ли модель с категории
                if (stripos($model, $categoryModel) === 0) {
                    $nextCharPos = strlen($categoryModel);
                    if ($nextCharPos >= strlen($model) || $model[$nextCharPos] === ' ') {
                        return $categoryName;
                    }
                }
                // Проверяем, начинается ли полная модель с категории
                if (stripos($fullModel, $categoryModel) === 0) {
                    $nextCharPos = strlen($categoryModel);
                    if ($nextCharPos >= strlen($fullModel) || $fullModel[$nextCharPos] === ' ') {
                        return $categoryName;
                    }
                }
            }
        }
    }

    // Если категория не определена, возвращаем пустую строку
    return '';
}

/**
 * 🚀 НОВАЯ ФУНКЦИЯ: Маппинг типов кузова для правильной фильтрации
 */
function getBodyTypeVariants($selectedType) {
    $bodyTypeMapping = [
        'Седан' => ['세단', '대형차', '중형차', '준중형', '준대형', 'седан', 'Седан'],
        'Хэтчбек' => ['해치백', '소형차', 'хэтчбек', 'Хэтчбек'],
        'Универсал' => ['왜건', '스테이션왜건', 'универсал', 'Универсал'],
        'Внедорожник' => ['SUV', '오프로드', 'CUV', 'внедорожник', 'Внедорожник'],
        'Кроссовер' => ['크로스오버', 'CUV', 'кроссовер', 'Кроссовер'],
        'Купе' => ['쿠페', '스포츠쿠페', 'купе', 'Купе'],
        'Кабриолет' => ['컨버터블', '카브리올레', '쿠페카브리올레', '오픈카', 'кабриолет', 'Кабриолет'],
        'Пикап' => ['픽업', 'пикап', 'Пикап'],
        'Минивэн' => ['미니밴', 'MPV', 'минивэн', 'Минивэн'],
        'Лимузин' => ['리무진', 'лимузин', 'Лимузин'],
        'Спорткар' => ['스포츠카', '슈퍼카', '하이퍼카', 'спорткар', 'Спорткар'],
        'Фургон' => ['밴', '화물', 'фургон', 'Фургон'],
        'Грузовик' => ['트럭', '화물', 'грузовик', 'Грузовик']
    ];

    return $bodyTypeMapping[$selectedType] ?? [$selectedType];
}

/**
 * Функция для фильтрации автомобилей
 */
function filterCars($cars, $filters) {
    $filtered = [];

    foreach ($cars as $car) {
        $match = true;

        // НОВОЕ: Фильтрация автомобилей с ошибочными ценами
        $price = (int)($car['price'] ?? 0);

        // Пропускаем забронированные автомобили
        if ($price === 999999 || $price === 333333) {
            $match = false;
        }

        // Пропускаем автомобили с ошибочными ценами (слишком низкие, нулевые или слишком высокие)
        if ($price <= 0 || ($price > 0 && $price < 100) || $price > 50000) {
            $match = false;
        }

        // Фильтр по марке
        if (!empty($filters['brand'])) {
            $carBrand = $car['brand'] ?? $car['mark'] ?? '';
            if (stripos($carBrand, $filters['brand']) === false) {
                $match = false;
            }
        }

        // Фильтр по модели
        if (!empty($filters['model'])) {
            $carModel = $car['model'] ?? '';
            if (stripos($carModel, $filters['model']) === false) {
                $match = false;
            }
        }

        // Фильтр по году
        if (!empty($filters['year_min'])) {
            $carYear = (int)($car['year'] ?? 0);
            if ($carYear < $filters['year_min']) {
                $match = false;
            }
        }

        if (!empty($filters['year_max'])) {
            $carYear = (int)($car['year'] ?? 0);
            if ($carYear > $filters['year_max']) {
                $match = false;
            }
        }

        // Фильтр по цене
        if (!empty($filters['price_min'])) {
            $carPrice = (int)($car['price'] ?? 0);
            if ($carPrice < $filters['price_min']) {
                $match = false;
            }
        }

        if (!empty($filters['price_max'])) {
            $carPrice = (int)($car['price'] ?? 0);
            if ($carPrice > $filters['price_max']) {
                $match = false;
            }
        }

        // НОВОЕ: Фильтр по категории (business, sport, suv)
        if (!empty($filters['category'])) {
            $carBrand = $car['brand'] ?? $car['mark'] ?? '';
            $carModel = $car['model'] ?? '';
            $carGeneration = $car['generation'] ?? '';
            $carCategory = determineCarCategory($carBrand, $carModel, $carGeneration);
            if ($carCategory !== $filters['category']) {
                $match = false;
            }
        }

        // 🚀 ИСПРАВЛЕННЫЙ ФИЛЬТР ПО ТИПУ КУЗОВА: Поиск по всем вариантам
        if (!empty($filters['body_type'])) {
            $carBodyType = $car['body_type'] ?? '';
            $bodyTypeVariants = getBodyTypeVariants($filters['body_type']);

            $bodyTypeMatch = false;
            foreach ($bodyTypeVariants as $variant) {
                if (stripos($carBodyType, $variant) !== false) {
                    $bodyTypeMatch = true;
                    break;
                }
            }

            if (!$bodyTypeMatch) {
                $match = false;
            }
        }

        if ($match) {
            $filtered[] = $car;
        }
    }

    return $filtered;
}

/**
 * Основная функция API
 */
function fastEncarAPI() {
    try {
        // Получаем параметры
        $requestedDate = $_GET['date'] ?? null;

        // Если дата не указана, ищем самую свежую
        if (empty($requestedDate)) {
            $date = findLatestCacheDate();
            if (empty($date)) {
                return [
                    'success' => false,
                    'error' => 'No cache data found. Please run cache generation first.',
                    'cache_status' => 'empty',
                    'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
                ];
            }
        } else {
            $date = $requestedDate;
        }

        $limit = (int)($_GET['limit'] ?? 20);
        $offset = (int)($_GET['offset'] ?? 0);
        $prioritySort = isset($_GET['priority_sort']) && $_GET['priority_sort'] === 'true';
        $loadAllCategory = isset($_GET['load_all_category']) && $_GET['load_all_category'] === 'true';
        $shuffleResults = isset($_GET['shuffle_results']) && $_GET['shuffle_results'] === 'true';

        // Фильтры
        $filters = [
            'brand' => $_GET['brand'] ?? '',
            'model' => $_GET['model'] ?? '',
            'year_min' => !empty($_GET['year_min']) ? (int)$_GET['year_min'] : null,
            'year_max' => !empty($_GET['year_max']) ? (int)$_GET['year_max'] : null,
            'price_min' => !empty($_GET['price_min']) ? (int)$_GET['price_min'] : null,
            'price_max' => !empty($_GET['price_max']) ? (int)$_GET['price_max'] : null,
            'category' => $_GET['category'] ?? '',
            'body_type' => $_GET['body_type'] ?? '',
        ];

        $result = [];

        // 🚀 НОВАЯ ЛОГИКА: Если запрашивается загрузка всех автомобилей категории
        if ($loadAllCategory && !empty($filters['category']) && in_array($filters['category'], ['business', 'sport', 'suv'])) {
            // Для премиум категорий загружаем ВСЕ автомобили из приоритетов 1-3
            $priorities = [1, 2, 3];
            $result = loadAllCacheByPriorities($priorities, $date, $filters);

            // Дополнительная фильтрация если нужно
            $result = filterCars($result, $filters);

            // Перемешиваем результаты если запрошено (для категорий business, sport, suv)
            if ($shuffleResults) {
                shuffle($result);
            }

            // Применяем offset и limit для пагинации
            $total = count($result);
            $paginatedResult = array_slice($result, $offset, $limit);

            return [
                'success' => true,
                'data' => $paginatedResult,
                'total' => $total,
                'count' => count($paginatedResult),
                'offset' => $offset,
                'limit' => $limit,
                'priority_sort' => $prioritySort,
                'load_all_category' => true,
                'shuffle_results' => $shuffleResults,
                'category' => $filters['category'],
                'priorities_loaded' => $priorities,
                'filters_applied' => array_filter($filters),
                'cache_source' => true,
                'cache_date' => $date,
                'requested_date' => $requestedDate,
                'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
            ];
        }

        // 🚀 НОВАЯ ЛОГИКА: Если запрашивается загрузка всех автомобилей для категории "all"
        if ($loadAllCategory && (empty($filters['category']) || $filters['category'] === 'all')) {
            // 🚀 ИСПРАВЛЕНО: Для категории "all" загружаем только приоритеты 1-3 (убираем бюджетные автомобили)
            $priorities = [1, 2, 3];

            // Убираем фильтр по категории для "all", чтобы загрузить все автомобили
            $allFilters = $filters;
            unset($allFilters['category']);

            $result = loadAllCacheByPriorities($priorities, $date, $allFilters);

            // Дополнительная фильтрация если нужно
            $result = filterCars($result, $allFilters);

            // Применяем offset и limit для пагинации
            $total = count($result);
            $paginatedResult = array_slice($result, $offset, $limit);

            return [
                'success' => true,
                'data' => $paginatedResult,
                'total' => $total,
                'count' => count($paginatedResult),
                'offset' => $offset,
                'limit' => $limit,
                'priority_sort' => $prioritySort,
                'load_all_category' => true,
                'shuffle_results' => $shuffleResults,
                'category' => 'all',
                'priorities_loaded' => $priorities,
                'filters_applied' => array_filter($allFilters),
                'cache_source' => true,
                'cache_date' => $date,
                'requested_date' => $requestedDate,
                'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
            ];
        }

        if ($prioritySort) {
            // 🚀 ПРИОРИТЕТНАЯ ЗАГРУЗКА: все приоритеты с оптимизированным чтением
            // 🚀 ИСПРАВЛЕНО: Убираем приоритет 4 (бюджетные автомобили) из ВСЕХ результатов
            $priorities = [1, 2, 3];

            foreach ($priorities as $priority) {
                // Вычисляем сколько еще нужно результатов
                $needed = ($limit + $offset) - count($result);
                if ($needed <= 0) {
                    break;
                }

                // Загружаем с оптимизированным чтением и фильтрацией на лету
                $cars = loadCacheByPriority($priority, $date, $filters, $needed * 2);

                if (!empty($cars)) {
                    // Дополнительная фильтрация если нужно (для сложных фильтров)
                    $filteredCars = filterCars($cars, $filters);

                    // Освобождаем память от исходного массива
                    unset($cars);

                    // Добавляем приоритет к каждому автомобилю
                    foreach ($filteredCars as &$car) {
                        $car['brand_priority'] = $priority;
                        $car['priority_category'] = getPriorityName($priority);
                    }

                    $result = array_merge($result, $filteredCars);

                    // Освобождаем память от отфильтрованного массива
                    unset($filteredCars);

                    // Принудительная очистка памяти
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }

                    // 🚀 ИСПРАВЛЕНО: Не прерываем досрочно при фильтрации по году
                    // Проверяем все приоритеты, чтобы найти все автомобили нужного года
                    $hasYearFilter = !empty($filters['year_min']) || !empty($filters['year_max']);
                    if (!$hasYearFilter && count($result) >= ($limit + $offset)) {
                        break;
                    }
                }
            }
        } else {
            // Обычная загрузка: загружаем все приоритеты и смешиваем
            // 🚀 ИСПРАВЛЕНО: Убираем приоритет 4 (бюджетные автомобили) из ВСЕХ результатов
            $maxPriority = 3;

            for ($priority = 1; $priority <= $maxPriority; $priority++) {
                $cars = loadCacheByPriority($priority, $date, $filters, $limit * 2);

                if (!empty($cars)) {
                    $filteredCars = filterCars($cars, $filters);
                    $result = array_merge($result, $filteredCars);

                    // Освобождаем память
                    unset($cars, $filteredCars);

                    // Принудительная очистка памяти
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }
                }
            }

            // Перемешиваем для обычного режима
            shuffle($result);
        }

        // Перемешиваем результаты если запрошено (только для категорий business, sport, suv при priority_sort=true)
        if ($shuffleResults && $prioritySort) {
            $isCategory = !empty($filters['category']) && in_array($filters['category'], ['business', 'sport', 'suv']);
            if ($isCategory) {
                shuffle($result);
            }
        }

        // Применяем offset и limit
        $total = count($result);
        $result = array_slice($result, $offset, $limit);
        
        // Возвращаем результат
        return [
            'success' => true,
            'data' => $result,
            'total' => $total,
            'count' => count($result),
            'offset' => $offset,
            'limit' => $limit,
            'priority_sort' => $prioritySort,
            'shuffle_results' => $shuffleResults,
            'filters_applied' => array_filter($filters),
            'cache_source' => true,
            'cache_date' => $date,
            'requested_date' => $requestedDate,
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
        ];
    }
}

/**
 * Функция для получения названия категории по приоритету
 */
function getPriorityName($priority) {
    $names = [
        1 => 'Супер премиум люкс',
        2 => 'Премиум',
        3 => 'Средние',
        4 => 'Бюджетные'
    ];
    
    return $names[$priority] ?? 'Неизвестная категория';
}

// Выполняем API
$response = fastEncarAPI();

// Всегда возвращаем полный объект ответа
echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
